<?php

namespace App\Modules\RequestDelete\Services;

use App\Mail\UserDeleteRequestMail;
use App\Modules\Client\Constants\DomainStatus;
use App\Modules\RequestDelete\Jobs\DomainEppCancellation;
use App\Util\Constant\UserDomainStatus;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class DomainDeleteService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        return new self;
    }

    public function createDeleteRequestSave($request)
    {
        $data = $request->all();

        // Get domain and user information
        $domainInfo = $this->getDomainInfo($data['domainId']);
        if (!$domainInfo) {
            throw new \Exception("Domain not found for ID: {$data['domainId']}");
        }

        $data['domainName'] = $domainInfo->domainName;
        $data['userID'] = $domainInfo->userID;
        $data['userEmail'] = $domainInfo->userEmail;
        $data['reason'] = $data['reason'] ?? 'Domain deletion request';
        $data['createdDate'] = now()->toDateTimeString();

        // Create the cancellation request record
        $this->createCancellationRequest($data);

        // Update domain status to IN_PROCESS
        $this->updateDomainStatus($data['domainId'], DomainStatus::IN_PROCESS);

        // Send notifications
        $this->sendCreateNotifications($data);
    }

    public function approveDeleteRequestSave($requestOrData)
    {
        $data = is_array($requestOrData) ? $requestOrData : $requestOrData->all();

        // Get domain information if not provided
        if (!isset($data['domainName']) || !isset($data['userEmail'])) {
            $domainInfo = $this->getDomainInfoFromRequest($data['domainId']);
            if (!$domainInfo) {
                throw new \Exception("Domain deletion request not found for domain ID: {$data['domainId']}");
            }

            $data['domainName'] = $domainInfo->domainName;
            $data['userID'] = $domainInfo->userID;
            $data['userEmail'] = $domainInfo->userEmail;
            $data['reason'] = $domainInfo->reason ?? 'Domain deletion request';
            $data['createdDate'] = $domainInfo->createdDate;
        }

        $this->processApproval($data);
    }

    public function rejectDeleteRequestSave($request)
    {
        $data = $request->all();

        $domainInfo = $this->getDomainInfoFromRequest($data['domainId']);
        if (!$domainInfo) {
            throw new \Exception("Domain deletion request not found for domain ID: {$data['domainId']}");
        }

        $data['domainName'] = $domainInfo->domainName;
        $data['userID'] = $domainInfo->userID;
        $data['userEmail'] = $domainInfo->userEmail;
        $data['reason'] = $domainInfo->reason ?? 'Domain deletion request';

        $this->processRejection($data);
    }

    // Helper methods for getting domain information
    private function getDomainInfo($domainId)
    {
        return DB::client()->table('domains')
            ->join('registered_domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', 'registered_domains.user_contact_registrar_id', '=', 'user_contacts.id')
            ->join('users', 'user_contacts.user_id', '=', 'users.id')
            ->where('domains.id', $domainId)
            ->select([
                'domains.name as domainName',
                'users.id as userID',
                'users.email as userEmail'
            ])
            ->first();
    }

    private function getDomainInfoFromRequest($domainId)
    {
        return DB::client()->table('domain_cancellation_requests')
            ->join('domains', 'domain_cancellation_requests.domain_id', '=', 'domains.id')
            ->join('users', 'domain_cancellation_requests.user_id', '=', 'users.id')
            ->where('domain_cancellation_requests.domain_id', $domainId)
            ->select([
                'domains.name as domainName',
                'domain_cancellation_requests.user_id as userID',
                'users.email as userEmail',
                'domain_cancellation_requests.reason',
                'domain_cancellation_requests.created_at as createdDate'
            ])
            ->first();
    }

    // Create operations
    private function createCancellationRequest($data)
    {
        DB::client()->table('domain_cancellation_requests')->insert([
            'domain_id' => $data['domainId'],
            'user_id' => $data['userID'],
            'reason' => $data['reason'],
            'requested_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    private function updateDomainStatus($domainId, $status)
    {
        DB::client()->table('domains')
            ->where('id', $domainId)
            ->update([
                'status' => $status,
                'updated_at' => now()
            ]);
    }

    private function sendCreateNotifications($data)
    {
        // Send email notification to user
        Mail::to($data['userEmail'])->send(new UserDeleteRequestMail($data['domainName']));
    }

    // Process approval
    private function processApproval($data)
    {
        $adminId = $this->getAdminId($data);
        $adminName = $this->getAdminName($data);
        $adminEmail = $this->getAdminEmail($data);
        $supportNote = $this->getApprovalSupportNote($data, $adminEmail);

        // Dispatch EPP job
        DomainEppCancellation::dispatch(
            $data['domainId'],
            $data['domainName'],
            $data['userID'],
            $data['userEmail'],
            $data['reason'] ?? 'Domain deletion request',
            $data['createdDate'] ?? now()->toDateTimeString(),
            $supportNote,
            $adminId,
            $adminName,
            $adminEmail
        );

        // Send notifications
        $this->userNotification($data);
        $this->userEmailNotification($data);
    }

    // Process rejection
    private function processRejection($data)
    {
        $adminId = $this->getAdminId($data);
        $adminName = $this->getAdminName($data);
        $supportNote = $data['support_note'] ?? "Request delete rejected by " . $this->getAdminEmail($data);

        $this->rejectDomainDeletionRequest($data, $adminId, $adminName, $supportNote);
    }

    // Admin helper methods
    private function getAdminId($data)
    {
        return (isset($data['is_scheduler']) && $data['is_scheduler']) ? 1 : Auth::id();
    }

    private function getAdminName($data)
    {
        return (isset($data['is_scheduler']) && $data['is_scheduler']) ? 'System' : (Auth::user()->name ?? 'System');
    }

    private function getAdminEmail($data)
    {
        return (isset($data['is_scheduler']) && $data['is_scheduler']) ? '<EMAIL>' : (Auth::user()->email ?? '<EMAIL>');
    }

    private function getApprovalSupportNote($data, $adminEmail)
    {
        return (isset($data['is_scheduler']) && $data['is_scheduler'])
            ? "Request delete automatically approved by {$adminEmail} (24+ hours)"
            : "Request delete approved by {$adminEmail}";
    }

    // Local delete method for EPP job service
    public function localDelete($requestData, bool $skipUpdate = false)
    {
        if (!$skipUpdate) {
            $this->updateDomainDeletionRequestTable($requestData);
        }

        $timestamp = now();

        $updates = [
            'status'     => UserDomainStatus::DELETED,
            'deleted_at' => $timestamp,
            'updated_at' => $timestamp,
        ];

        DB::client()->table('registered_domains')
            ->where('domain_id', $requestData['domainId'])
            ->update($updates);

        DB::client()->table('domains')
            ->where('id', $requestData['domainId'])
            ->update($updates);
    }

    private function rejectDomainDeletionRequest(array $data, int $adminId, string $adminName, string $supportNote): void
    {
        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $data['domainId'])
            ->update([
                'support_agent_id' => $adminId,
                'support_agent_name' => $adminName . ' (' . Auth::user()->email . ')',
                'support_note' => $supportNote,
                'feedback_date' => now(),
                // 'updated_at' => now(),
            ]);

        $this->reactivateDomain($data['domainId']);

        $this->sendRejectionNotification($data, $adminName);

        $this->logDomainRejectionHistory($data, $adminName);
    }

    private function sendRejectionNotification(array $data, string $adminName): void
    {
        $message = 'Your request to delete the domain "' . $data['domainName'] . '" has been rejected by ' . $adminName . '. The domain has been reactivated and is now available for use.';

        DB::client()->table('notifications')->insert([
            'user_id' => $data['userID'],
            'title' => 'Domain Deletion Request Rejected',
            'redirect_url' => '/domain',
            'message' => $message,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    private function logDomainRejectionHistory(array $data, string $adminName): void
    {
        $adminEmail = Auth::user()->email ?? '<EMAIL>';
        $message = 'Domain "' . $data['domainName'] . '" deletion request rejected and domain reactivated by ' . $adminName . ' (' . $adminEmail . ')';

        DB::client()->table('domain_transaction_histories')->insert([
            'domain_id' => $data['domainId'],
            'type' => 'DOMAIN_DELETE_REJECTED',
            'user_id' => $data['userID'],
            'status' => 'success',
            'message' => $message,
            'payload' => json_encode($data),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    private function reactivateDomain(int $domainId): void
    {
        $timestamp = now();

        DB::client()->table('domains')
            ->where('id', $domainId)
            ->update([
                'status' => DomainStatus::ACTIVE,
                'updated_at' => $timestamp,
            ]);

        DB::client()->table('registered_domains')
            ->where('domain_id', $domainId)
            ->update([
                'status' => UserDomainStatus::OWNED,
                'updated_at' => $timestamp,
            ]);
    }

    public function updateDomainDeletionRequestTable($requestData, $authID = null)
    {
        $exists = DB::client()
            ->table('domain_cancellation_requests')
            ->where('domain_id', $requestData['domainId'])
            ->exists();

        if (!$exists) {
            return;
        }

        $date = Carbon::parse($requestData['createdDate']);
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        if (isset($requestData['is_scheduler']) && $requestData['is_scheduler']) {
            $agentID = 1;
            $agentName = 'System (<EMAIL>)';
            $supportNote = 'Request delete automatically <NAME_EMAIL> (24+ hours)';
        } else {
            $agentID = $authID ?? Auth::id();
            $agentName = Auth::user()->name . ' (' . Auth::user()->email . ')';
            $supportNote = 'Request delete approved by ' . Auth::user()->email;
        }

        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $requestData['domainId'])
            ->update([
                'support_agent_id'   => $agentID,
                'support_agent_name' => $agentName,
                'deleted_at'         => now(),
                'feedback_date'      => now(),
                'support_note'       => $supportNote,
                'is_refunded'        => $is_refunded,
            ]);
    }

    private function userNotification($requestData)
    {
        $userID = $requestData['userID'];
        $domainName = $requestData['domainName'];

        if (!$userID || !$domainName) return;

        $message = (isset($requestData['is_scheduler']) && $requestData['is_scheduler'])
            ? 'Your request to delete the domain "' . $domainName . '" has been automatically approved.'
            : 'Your request to delete the domain "' . $domainName . '" has been approved.';

        DB::client()->table('notifications')->insert([
            'user_id'      => $userID,
            'title'        => 'Domain Deletion Request Approved',
            'message'      => $message,
            'redirect_url' => '/domain',
            'created_at'   => now(),
            'importance'   => 'important',
        ]);
    }

    private function userEmailNotification($requestData)
    {
        $domainName = $requestData['domainName'];
        $userEmail = $requestData['userEmail'];

        $body = (isset($requestData['is_scheduler']) && $requestData['is_scheduler'])
            ? 'Your request to delete the domain "' . $domainName . '" has been approved. The domain will be removed from your account and queued for deletion shortly. This action is final and cannot be undone.'
            : 'Your request to delete the domain "' . $domainName . '" has been approved. The domain will be removed from your account and queued for deletion shortly. This action is final and cannot be undone.';

        $message = [
            'subject'  => 'Domain Deletion Request Approved',
            'greeting' => 'Greetings!',
            'body'     => $body,
            'text'     => Carbon::now()->format('Y-m-d H:i:s'),
            'sender'   => 'StrangeDomains Support',
        ];

        Mail::to($userEmail)->send(new UserDeleteRequestMail($message));

        self::emailTrack($userEmail, $message, $requestData['domainId']);
    }

    private function getUserByDomain($domainId) 
    {
        return DB::client()->table('registered_domains')
            ->select('users.id as user_id', 'users.email', 'users.first_name', 'users.last_name')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->where('domains.id', $domainId)
            ->first();
    }

    private function emailTrack($email, array $payload, $domainId = null) 
    {
        $userId = null;
        $userName = null;

        if ($domainId) {
            $user = $this->getUserByDomain($domainId);
            if ($user) {
                $userId = $user->user_id;
                $userName = $user->first_name . ' ' . $user->last_name;
            }
        }

        $emailSent = DB::client()->table('email_histories')
            ->insert([
                'user_id' => $userId,
                'name' => $userName ?? 'System',
                'recipient_email' => $email,
                'subject' => 'Domain Deletion Request Approved',
                'email_type' => 'Domain Deletion Request Approved',
                'email_body' => json_encode($payload),
                'attachment' => null,
                'created_at' => now(),
                'updated_at' => now()
            ]);

        return $emailSent;
    }

    public function processExpiredRequests()
    {
        $expiredRequests = DB::client()->table('domain_cancellation_requests')
            ->join('domains', 'domain_cancellation_requests.domain_id', '=', 'domains.id')
            ->join('users', 'domain_cancellation_requests.user_id', '=', 'users.id')
            ->select([
                'domain_cancellation_requests.domain_id as domainId',
                'domains.name as domainName',
                'domain_cancellation_requests.user_id as userID',
                'users.email as userEmail',
                'domain_cancellation_requests.reason',
                'domain_cancellation_requests.requested_at as createdDate'
            ])
            ->where('domains.status', 'IN_PROCESS')
            ->whereNull('domain_cancellation_requests.support_agent_id')
            ->where('domain_cancellation_requests.requested_at', '<=', now()->subHours(24))
            ->get();

        foreach ($expiredRequests as $request) {
            $data = [
                'domainId' => $request->domainId,
                'domainName' => $request->domainName,
                'userID' => $request->userID,
                'userEmail' => $request->userEmail,
                'reason' => $request->reason,
                'createdDate' => $request->createdDate,
                'is_scheduler' => true,
            ];

            $this->approveDeleteRequestSave($data);
        }

        return count($expiredRequests);
    }
}
