<?php

namespace App\Modules\RequestDelete\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\RequestDelete\Requests\CreateDeleteRequest;
use App\Modules\RequestDelete\Requests\ShowListRequest;
use App\Modules\RequestDelete\Requests\ApproveDeleteRequest;
use App\Modules\RequestDelete\Requests\RejectDeleteRequest;
use Inertia\Inertia;

class DeleteRequestController extends Controller
{
    public function index(ShowListRequest $request)
    {
        return Inertia::render('RequestDelete/Index', $request->show());
    }

    public function store(ShowListRequest $request){
        $request->feedbackSave();
    }

    public function approve_delete(ApproveDeleteRequest $request)
    {
        $request->approve();
    }

    public function reject_delete(RejectDeleteRequest $request)
    {
        $request->reject();
    }

    public function create_delete(CreateDeleteRequest $request){
        // $data = $request->all();
        // return response()->json(['message' => $request->all()]);
        $request->create_deleteRequest();
    }
}