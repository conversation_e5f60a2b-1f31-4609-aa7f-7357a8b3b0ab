<?php

namespace App\Modules\RequestDelete\Requests;

// use App\Modules\PendingDelete\Constants\StatusTypes;

use App\Modules\RequestDelete\Constants\StatusTypes;
use App\Modules\RequestDelete\Services\DatabaseQueryService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ShowListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    { 

        //Check the route or context to apply rules only for supportNoteSave if needed
        if ($this->routeIs('domain.support-feedback.store')) {
            return [
                'support_note' => ['required', 'string', 'min:10'],
            ];
        }

        return [
            'statusType' => ['string', Rule::in(StatusTypes::TYPES)],
            'orderby' => ['string', Rule::in([
                'domain:desc',
                'domain:asc',
                'dateDeleted:desc',
                'dateDeleted:asc',
            ])],
            'domain' => ['string'],
            'email' => ['email', 'max:255'],
            'deletedBy' => ['string'],
            'support_note' => ['string', 'min:10'],
        ];
    }

    public function show()
    {
        return DatabaseQueryService::instance()->get($this);
    }

    public function feedbackSave()
    {
        return DatabaseQueryService::instance()->supportNoteSave($this);
    }
}
